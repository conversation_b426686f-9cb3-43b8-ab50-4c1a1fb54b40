import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Component, Input } from '@angular/core';
import { ManagedAccountDataTableComponent } from './managed-account-data-table.component';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ManagedAccountService } from '../../managed-account.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of } from 'rxjs';

// Mock child component
@Component({
  selector: 'app-data-table-footnote',
  template: '<div></div>',
})
class MockDataTableFootnoteComponent {
  @Input() footnotes: any[];
}

// Mock services
const mockManagedAccountService = {
  getManagedCapTableConfig: jasmine.createSpy('getManagedCapTableConfig').and.returnValue(
    of({
      capTablePeriods: [
        { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false },
        { periodId: 2, period: 'Q1 2024', isMonthly: false, isQuarterly: true, isAnnually: false },
        { periodId: 3, period: '2024', isMonthly: false, isQuarterly: false, isAnnually: true }
      ],
      latestPeriod: { periodId: 1, period: 'Jan 2024', isMonthly: true, isQuarterly: false, isAnnually: false }
    })
  )
};

const mockMiscellaneousService = {
  // Add any methods that might be needed
};

describe('ManagedAccountDataTableComponent', () => {
  let component: ManagedAccountDataTableComponent;
  let fixture: ComponentFixture<ManagedAccountDataTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        ManagedAccountDataTableComponent,
        MockDataTableFootnoteComponent
      ],
      imports: [
        KendoModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot(),
        HttpClientTestingModule
      ],
      providers: [
        NgbModal,
        { provide: ManagedAccountService, useValue: mockManagedAccountService },
        { provide: MiscellaneousService, useValue: mockMiscellaneousService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;
    
    // Set required inputs before detectChanges to ensure ngOnInit runs with proper values
    component.managedAccountId = 'test-id';
    component.moduleName = 'test-module';
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.tableTitle).toBe('');
    expect(component.data).toEqual([]);
    expect(component.tableName).toBe('');
    expect(component.footnotes).toBeDefined();
    expect(component.footnotes.length).toBe(1);
  });

  it('should accept input properties', () => {
    const testTitle = 'Test Table';
    const testData = [{ id: 1, name: 'Test' }];
    const testName = 'test_table';

    component.tableTitle = testTitle;
    component.data = testData;
    component.tableName = testName;

    expect(component.tableTitle).toBe(testTitle);
    expect(component.data).toEqual(testData);
    expect(component.tableName).toBe(testName);
  });

  it('should load cap table configuration and group period data by frequency', fakeAsync(() => {
    // The method should have been called automatically in ngOnInit
    // Wait for the async operation to complete
    tick();

    // Verify the service was called
    expect(mockManagedAccountService.getManagedCapTableConfig).toHaveBeenCalledWith('test-id', 'test-module');

    // Verify periodData is grouped correctly
    expect(component.periodData).toBeDefined();
    expect(Array.isArray(component.periodData)).toBe(true);

    // Verify hasCapTableData is set correctly
    expect(component.hasCapTableData).toBe(true);

    // Verify selectedPeriod is set to latestPeriod
    expect(component.selectedPeriod).toEqual({
      periodId: 1,
      period: 'Jan 2024',
      isMonthly: true,
      isQuarterly: false,
      isAnnually: false
    });
    expect(component.periodId).toBe(1);
  }));
  
  it('should handle empty cap table periods', fakeAsync(() => {
    // Mock service to return empty periods
    mockManagedAccountService.getManagedCapTableConfig.and.returnValue(
      of({ capTablePeriods: [], latestPeriod: null })
    );

    // Recreate component with new mock to test empty periods scenario
    fixture = TestBed.createComponent(ManagedAccountDataTableComponent);
    component = fixture.componentInstance;
    component.managedAccountId = 'test-id';
    component.moduleName = 'test-module';
    fixture.detectChanges();

    tick();

    expect(component.hasCapTableData).toBe(false);
    expect(component.selectedPeriod).toBe(null);
    expect(component.periodId).toBe(0);
  }));

  it('should return correct disabled state for period combobox', () => {
    // Test when periodData is empty
    component.periodData = [];
    expect(component.isPeriodComboboxDisabled).toBe(true);

    // Test when periodData has items
    component.periodData = [{ value: 'test' }];
    expect(component.isPeriodComboboxDisabled).toBe(false);

    // Test when periodData is null
    component.periodData = null;
    expect(component.isPeriodComboboxDisabled).toBe(true);
  });
});
